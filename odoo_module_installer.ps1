# === Settings === 
$token    = "*********************************************************************************************" 
$repo     = "https://$<EMAIL>/cubesteam/kayan_erp.git" 
 
# === Detect Odoo Services === 
$odooServices = Get-Service | Where-Object { $_.Name -like "*odoo*" } 
 
if ($odooServices.Count -eq 0) { 
    Write-Host "❌ No Odoo services found." 
    $selectedService = Read-Host "Enter the Odoo service name manually" 
} elseif ($odooServices.Count -eq 1) { 
    $selectedService = $odooServices[0].Name 
    Write-Host "✅ Found single Odoo service: $selectedService" 
} else { 
    Write-Host "Multiple Odoo services detected:" 
    for ($i=0; $i -lt $odooServices.Count; $i++) { 
        Write-Host "$($i+1). $($odooServices[$i].DisplayName) [$($odooServices[$i].Name)]" 
    } 
    $selection = Read-Host "Enter the number of the Odoo instance you want to work with" 
    $index = [int]$selection - 1 
    if ($index -ge 0 -and $index -lt $odooServices.Count) { 
        $selectedService = $odooServices[$index].Name 
    } else { 
        Write-Host "❌ Invalid selection. Exiting." 
        exit 
    } 
} 
 
# === Detect Odoo Path via NSSM === 
$nssmPath = "C:\odoo18\nssm\win64\nssm.exe"  # Default location, adjust if different 
$odooBase = $null 
try { 
    if (Test-Path $nssmPath) { 
        $odooExe = & $nssmPath get $selectedService Application 
        if ($odooExe) { 
            $odooBase = Split-Path $odooExe -Parent | Split-Path -Parent 
            Write-Host "✅ Auto-detected Odoo path: $odooBase" 
        } 
    } 
} catch { 
    Write-Host "⚠️ Auto-detection failed." 
} 
 
# === Ask user if detection failed === 
if (-not $odooBase) { 
    $odooBase = Read-Host "Enter your Odoo installation path manually (default: C:\odoo18)" 
    if ([string]::IsNullOrWhiteSpace($odooBase)) { 
        $odooBase = "C:\odoo18" 
    } 
} 
 
# === Define Paths === 
$addonsPath = Join-Path $odooBase "server\custom_addons" 
$confFile   = Join-Path $odooBase "server\odoo.conf" 
 
# === Ensure addons folder exists === 
if (!(Test-Path $addonsPath)) { 
    New-Item -ItemType Directory -Force -Path $addonsPath | Out-Null 
    Write-Host "Created folder: $addonsPath" 
} 
 
# === Clone or Pull Repo directly into custom_addons === 
if (Test-Path (Join-Path $addonsPath ".git")) { 
    Write-Host "Updating kayan_erp repo ..." 
    Set-Location $addonsPath 
    git pull 
} else { 
    Write-Host "Cloning kayan_erp repo into $addonsPath ..." 
    git clone $repo $addonsPath 
} 
 
# === Update odoo.conf === 
if (Test-Path $confFile) { 
    # Backup existing conf 
    Copy-Item $confFile ($confFile + ".bak") -Force 
    Write-Host "Backup created: $confFile.bak" 
 
    $conf = Get-Content $confFile 
    $addonsLineIndex = ($conf | Select-String "addons_path").LineNumber - 1 
 
    if ($addonsLineIndex -ge 0) { 
        if ($conf[$addonsLineIndex] -match [regex]::Escape($addonsPath)) { 
            Write-Host "addons_path already includes $addonsPath" 
        } else { 
            $conf[$addonsLineIndex] = $conf[$addonsLineIndex] + ",$addonsPath" 
            $conf | Set-Content $confFile -Encoding UTF8 
            Write-Host "Updated addons_path in $confFile" 
        } 
    } else { 
        Add-Content $confFile "addons_path = $addonsPath" 
        Write-Host "Added new addons_path entry to $confFile" 
    } 
} else { 
    Write-Host "❌ Could not find odoo.conf at $confFile" 
} 
 
# === Restart Odoo service === 
Write-Host "Restarting Odoo service: $selectedService ..." 
Restart-Service -Name $selectedService -Force 
 
Write-Host "✅ Done. kayan_erp modules are inside $addonsPath and Odoo restarted." 
