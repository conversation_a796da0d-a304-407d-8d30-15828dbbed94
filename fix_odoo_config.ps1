# Fix Odoo Configuration - Run as Administrator
param(
    [string]$OdooPath = "C:\Program Files\Odoo 19.0.20250927"
)

$confFile = Join-Path $OdooPath "server\odoo.conf"
$backupFile = "$confFile.bak.20250927_204242"

Write-Host "Fixing Odoo configuration..." -ForegroundColor Yellow

# Check if backup exists
if (Test-Path $backupFile) {
    Write-Host "Restoring from backup..." -ForegroundColor Green
    Copy-Item $backupFile $confFile -Force
} else {
    Write-Host "No backup found, fixing current config..." -ForegroundColor Yellow
}

# Read and fix the config
$conf = Get-Content $confFile
$fixedConf = @()

foreach ($line in $conf) {
    if ($line -match "^\s*addons_path\s*=") {
        # Fix the addons_path line - make it all on one line
        $addonsPath = Join-Path $OdooPath "server\custom_addons"
        $fixedLine = "addons_path = c:\program files\odoo 19.0.20250927\server\odoo\addons,$addonsPath"
        $fixedConf += $fixedLine
        Write-Host "Fixed addons_path line" -ForegroundColor Green
    } elseif ($line -match "Files\\Odoo.*custom_addons") {
        # Skip broken continuation lines
        Write-Host "Skipping broken line: $line" -ForegroundColor Yellow
    } else {
        $fixedConf += $line
    }
}

# Write the fixed config
$fixedConf | Set-Content $confFile -Encoding UTF8

Write-Host "Configuration fixed!" -ForegroundColor Green
Write-Host "Now try starting the Odoo service" -ForegroundColor Cyan
