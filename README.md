# Odoo Custom Module Auto-Installer

This PowerShell script automates the installation and updating of custom Odoo modules from a GitHub repository.

## 🔒 Security Improvements

### Original Script Issues Fixed:
- ❌ **Hardcoded GitHub token** (major security risk)
- ❌ **No error handling** for git operations
- ❌ **No validation** of paths and services
- ❌ **No permission checks**

### New Security Features:
- ✅ **Secure token handling** (environment variables, secure prompts)
- ✅ **Administrator privilege checks**
- ✅ **Comprehensive error handling**
- ✅ **Path and service validation**
- ✅ **Backup creation with timestamps**

## 📋 Prerequisites

1. **PowerShell 5.1+** (Windows PowerShell or PowerShell Core)
2. **Git** installed and available in PATH
3. **Administrator privileges** (required to restart services)
4. **GitHub Personal Access Token** (for private repositories)

## 🚀 Usage

### Method 1: Environment Variable (Recommended)
```powershell
# Set GitHub token as environment variable (secure)
$env:GITHUB_TOKEN = "your_github_token_here"

# Run the script
.\odoo_module_installer_improved.ps1
```

### Method 2: Command Line Parameters
```powershell
# With all parameters
.\odoo_module_installer_improved.ps1 -GitHubToken "your_token" -OdooPath "C:\odoo18" -ServiceName "odoo-server" -SkipRestart

# With some parameters (script will prompt for missing ones)
.\odoo_module_installer_improved.ps1 -OdooPath "C:\custom\odoo"

# Minimal (script will auto-detect everything)
.\odoo_module_installer_improved.ps1
```

### Method 3: Interactive Mode
```powershell
# Run without parameters - script will prompt for everything
.\odoo_module_installer_improved.ps1
```

## 📝 Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `GitHubToken` | String | GitHub Personal Access Token | Prompts if not provided |
| `OdooPath` | String | Odoo installation directory | Auto-detects via NSSM |
| `ServiceName` | String | Odoo Windows service name | Auto-detects |
| `SkipRestart` | Switch | Skip service restart | False |

## 🔧 What the Script Does

1. **Pre-flight Checks**
   - Verifies administrator privileges
   - Checks Git availability
   - Validates GitHub token

2. **Service Detection**
   - Auto-detects Odoo services
   - Allows manual selection for multiple services
   - Validates service exists

3. **Path Detection**
   - Uses NSSM to find Odoo installation
   - Validates paths contain actual Odoo installation
   - Creates custom_addons directory if needed

4. **Repository Management**
   - Clones repository if not present
   - Updates existing repository
   - Handles conflicts and errors gracefully

5. **Configuration Update**
   - Creates timestamped backup of odoo.conf
   - Updates addons_path safely
   - Preserves existing configuration

6. **Service Management**
   - Restarts Odoo service
   - Verifies service status
   - Provides clear feedback

## 🛡️ Security Best Practices

### GitHub Token Management
```powershell
# Option 1: Set as user environment variable (persistent)
[Environment]::SetEnvironmentVariable("GITHUB_TOKEN", "your_token", "User")

# Option 2: Set for current session only
$env:GITHUB_TOKEN = "your_token"

# Option 3: Use secure prompt (script will handle this)
# Just run the script without token - it will prompt securely
```

### Token Permissions Required
Your GitHub token needs these permissions:
- `repo` (for private repositories)
- `read:org` (if repository is in an organization)

## 🔍 Troubleshooting

### Common Issues

1. **"Access Denied" when restarting service**
   - Run PowerShell as Administrator
   - Check if user has service management permissions

2. **Git clone fails**
   - Verify GitHub token has correct permissions
   - Check if repository URL is correct
   - Ensure Git is installed and in PATH

3. **Odoo path not detected**
   - Manually specify path with `-OdooPath` parameter
   - Check if NSSM is installed in expected location

4. **Service not found**
   - Use `Get-Service | Where-Object { $_.Name -like "*odoo*" }` to list services
   - Manually specify with `-ServiceName` parameter

### Logs and Debugging
The script provides colored output for easy debugging:
- 🔴 **Red**: Errors that stop execution
- 🟡 **Yellow**: Warnings and prompts
- 🟢 **Green**: Success messages
- 🔵 **Cyan**: Information messages

## 📁 File Structure After Installation

```
C:\odoo18\
├── server\
│   ├── custom_addons\          # Your modules here
│   │   ├── .git\               # Git repository
│   │   ├── module1\
│   │   ├── module2\
│   │   └── ...
│   ├── odoo.conf               # Updated configuration
│   └── odoo.conf.bak.YYYYMMDD_HHMMSS  # Timestamped backup
```

## 🔄 Updating Modules

To update modules, simply run the script again:
```powershell
.\odoo_module_installer_improved.ps1
```

The script will:
1. Detect existing repository
2. Pull latest changes
3. Restart Odoo service
4. Preserve your configuration

## ⚠️ Important Notes

- Always test in a development environment first
- The script creates automatic backups of odoo.conf
- Service restart may cause brief downtime
- Use `-SkipRestart` if you want to restart manually
