# Odoo Custom Module Auto-Installer
# Clean version without emojis for better compatibility

param(
    [string]$GitHubToken,
    [string]$OdooPath,
    [string]$ServiceName,
    [switch]$SkipRestart
)

# === Configuration ===
$REPO_URL = "https://github.com/cubesteam/kayan_erp.git"
$DEFAULT_ODOO_PATH = "C:\odoo16"
$DEFAULT_NSSM_PATH = "C:\odoo16\nssm\win64\nssm.exe"

# === Functions ===
function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Test-GitAvailable {
    try {
        git --version | Out-Null
        return $true
    } catch {
        return $false
    }
}

function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Get-GitHubToken {
    if ($GitHubToken) {
        return $GitHubToken
    }
    
    # Try to get from environment variable
    $envToken = $env:GITHUB_TOKEN
    if ($envToken) {
        Write-ColorOutput "SUCCESS: Using GitHub token from environment variable" "Green"
        return $envToken
    }
    
    # Prompt user securely
    Write-ColorOutput "WARNING: GitHub token required for private repository access" "Yellow"
    Write-ColorOutput "Please enter your GitHub Personal Access Token:" "Yellow"
    $secureToken = Read-Host -AsSecureString
    $ptr = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($secureToken)
    try {
        return [System.Runtime.InteropServices.Marshal]::PtrToStringBSTR($ptr)
    } finally {
        [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($ptr)
    }
}

# === Pre-flight Checks ===
Write-ColorOutput "Starting Odoo Module Installer..." "Cyan"

# Check if running as administrator (only required if not skipping restart)
if (-not $SkipRestart -and -not (Test-AdminRights)) {
    Write-ColorOutput "ERROR: This script requires administrator privileges to restart services." "Red"
    Write-ColorOutput "Please run PowerShell as Administrator and try again, or use -SkipRestart parameter." "Yellow"
    exit 1
}

# Check if git is available
if (-not (Test-GitAvailable)) {
    Write-ColorOutput "ERROR: Git is not installed or not available in PATH." "Red"
    Write-ColorOutput "Please install Git and try again." "Yellow"
    exit 1
}

# === Get GitHub Token ===
try {
    $token = Get-GitHubToken
    $repoWithAuth = $REPO_URL -replace "https://", "https://$token@"
} catch {
    Write-ColorOutput "ERROR: Failed to get GitHub token: $($_.Exception.Message)" "Red"
    exit 1
}

# === Detect Odoo Services ===
Write-ColorOutput "Detecting Odoo services..." "Yellow"

if ($ServiceName) {
    $selectedService = $ServiceName
    Write-ColorOutput "SUCCESS: Using provided service name: $selectedService" "Green"
} else {
    $odooServices = Get-Service | Where-Object { $_.Name -like "*odoo*" }
    
    if ($odooServices.Count -eq 0) {
        Write-ColorOutput "ERROR: No Odoo services found." "Red"
        $selectedService = Read-Host "Enter the Odoo service name manually"
        if ([string]::IsNullOrWhiteSpace($selectedService)) {
            Write-ColorOutput "ERROR: No service name provided. Exiting." "Red"
            exit 1
        }
    } elseif ($odooServices.Count -eq 1) {
        $selectedService = $odooServices[0].Name
        Write-ColorOutput "SUCCESS: Found single Odoo service: $selectedService" "Green"
    } else {
        Write-ColorOutput "Multiple Odoo services detected:" "Yellow"
        for ($i = 0; $i -lt $odooServices.Count; $i++) {
            Write-ColorOutput "$($i+1). $($odooServices[$i].DisplayName) [$($odooServices[$i].Name)]" "White"
        }
        $selection = Read-Host "Enter the number of the Odoo instance you want to work with"
        $index = [int]$selection - 1
        if ($index -ge 0 -and $index -lt $odooServices.Count) {
            $selectedService = $odooServices[$index].Name
        } else {
            Write-ColorOutput "ERROR: Invalid selection. Exiting." "Red"
            exit 1
        }
    }
}

# Verify service exists
try {
    $service = Get-Service -Name $selectedService -ErrorAction Stop
    Write-ColorOutput "SUCCESS: Service verified: $($service.DisplayName)" "Green"
} catch {
    Write-ColorOutput "ERROR: Service '$selectedService' not found." "Red"
    exit 1
}

# === Detect Odoo Path ===
Write-ColorOutput "Detecting Odoo installation path..." "Yellow"

if ($OdooPath) {
    $odooBase = $OdooPath
    Write-ColorOutput "SUCCESS: Using provided Odoo path: $odooBase" "Green"
} else {
    $odooBase = $null
    
    # Try to detect Odoo path using Windows service information
    try {
        # Get service executable path from Windows registry/WMI
        $serviceInfo = Get-WmiObject -Class Win32_Service | Where-Object { $_.Name -eq $selectedService }
        if ($serviceInfo -and $serviceInfo.PathName) {
            $servicePath = $serviceInfo.PathName.Trim().Trim('"')

            # If it's NSSM, extract the actual application path
            if ($servicePath -like "*nssm.exe*") {
                # Use the NSSM to query the actual application
                $nssmOutput = & $servicePath get $selectedService Application 2>$null
                if ($nssmOutput) {
                    $lines = $nssmOutput -split "`n" | Where-Object { $_.Trim() -ne "" }

                    foreach ($line in $lines) {
                        $cleanLine = $line.Trim().Trim('"')
                        if ($cleanLine -like "*python.exe" -and $cleanLine -notlike "*Access is denied*" -and $cleanLine.Length -gt 10) {
                            # Extract Odoo base path from python.exe path
                            # From "C:\Program Files\Odoo 19.0.20250927\python\python.exe"
                            # Get "C:\Program Files\Odoo 19.0.20250927"
                            $pythonDir = Split-Path $cleanLine -Parent
                            $odooBase = Split-Path $pythonDir -Parent
                            Write-ColorOutput "SUCCESS: Auto-detected Odoo path: $odooBase" "Green"
                            break
                        }
                    }
                }
            } else {
                # Direct service path - extract Odoo base from it
                if ($servicePath -like "*python.exe*") {
                    $pythonDir = Split-Path $servicePath -Parent
                    $odooBase = Split-Path $pythonDir -Parent
                    Write-ColorOutput "SUCCESS: Auto-detected Odoo path: $odooBase" "Green"
                }
            }
        }
    } catch {
        Write-ColorOutput "Auto-detection failed. Please enter path manually." "Yellow"
    }
    
    # Ask user if detection failed
    if (-not $odooBase) {
        $userInput = Read-Host "Enter your Odoo installation path (default: $DEFAULT_ODOO_PATH)"
        if ([string]::IsNullOrWhiteSpace($userInput)) {
            $odooBase = $DEFAULT_ODOO_PATH
        } else {
            $odooBase = $userInput
        }
    }
}

# Validate Odoo path
if (-not (Test-Path $odooBase)) {
    Write-ColorOutput "ERROR: Odoo path does not exist: $odooBase" "Red"
    exit 1
}

$serverPath = Join-Path $odooBase "server"
if (-not (Test-Path $serverPath)) {
    Write-ColorOutput "ERROR: Server directory not found in: $odooBase" "Red"
    Write-ColorOutput "This doesn't appear to be a valid Odoo installation." "Yellow"
    exit 1
}

# === Define Paths ===
$addonsPath = Join-Path $odooBase "server\custom_addons"
$confFile = Join-Path $odooBase "server\odoo.conf"

Write-ColorOutput "Addons path: $addonsPath" "Cyan"
Write-ColorOutput "Config file: $confFile" "Cyan"

# === Ensure addons folder exists ===
if (!(Test-Path $addonsPath)) {
    try {
        New-Item -ItemType Directory -Force -Path $addonsPath | Out-Null
        Write-ColorOutput "SUCCESS: Created addons folder: $addonsPath" "Green"
    } catch {
        Write-ColorOutput "ERROR: Failed to create addons folder: $($_.Exception.Message)" "Red"
        exit 1
    }
}

# === Clone or Pull Repository ===
Write-ColorOutput "Managing repository..." "Yellow"

$gitDir = Join-Path $addonsPath ".git"
$currentLocation = Get-Location

try {
    if (Test-Path $gitDir) {
        Write-ColorOutput "Updating existing repository..." "Yellow"
        Set-Location $addonsPath
        
        # Check if we're in the right repository
        $remoteUrl = git remote get-url origin 2>$null
        if ($remoteUrl -and $remoteUrl -like "*kayan_erp*") {
            git pull
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "SUCCESS: Repository updated successfully" "Green"
            } else {
                Write-ColorOutput "WARNING: Git pull completed with warnings" "Yellow"
            }
        } else {
            Write-ColorOutput "WARNING: Existing repository is not kayan_erp. Manual intervention required." "Yellow"
            Write-ColorOutput "Current remote: $remoteUrl" "White"
        }
    } else {
        Write-ColorOutput "Cloning repository..." "Yellow"
        
        # Check if directory is empty
        $existingFiles = Get-ChildItem $addonsPath -Force
        if ($existingFiles.Count -gt 0) {
            Write-ColorOutput "WARNING: Custom addons directory is not empty. Contents:" "Yellow"
            $existingFiles | ForEach-Object { Write-ColorOutput "  - $($_.Name)" "White" }
            $confirm = Read-Host "Continue with clone? This may overwrite files (y/N)"
            if ($confirm -ne 'y' -and $confirm -ne 'Y') {
                Write-ColorOutput "ERROR: Operation cancelled by user." "Red"
                exit 1
            }
        }
        
        git clone $repoWithAuth $addonsPath
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "SUCCESS: Repository cloned successfully" "Green"
        } else {
            Write-ColorOutput "ERROR: Failed to clone repository (Exit code: $LASTEXITCODE)" "Red"
            Write-ColorOutput "This could be due to:" "Yellow"
            Write-ColorOutput "1. Invalid or expired GitHub token" "White"
            Write-ColorOutput "2. Token doesn't have 'repo' scope for private repositories" "White"
            Write-ColorOutput "3. Repository doesn't exist or you don't have access" "White"
            Write-ColorOutput "4. Network connectivity issues" "White"
            Write-ColorOutput "`nPlease check your GitHub token and repository access." "Yellow"
            exit 1
        }
    }
} catch {
    Write-ColorOutput "ERROR: Git operation failed: $($_.Exception.Message)" "Red"
    exit 1
} finally {
    Set-Location $currentLocation
}

# === Update odoo.conf ===
Write-ColorOutput "Updating Odoo configuration..." "Yellow"

if (Test-Path $confFile) {
    try {
        # Create backup
        $backupFile = "$confFile.bak.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Copy-Item $confFile $backupFile -Force
        Write-ColorOutput "SUCCESS: Backup created: $backupFile" "Green"
        
        $conf = Get-Content $confFile
        $addonsLineFound = $false
        
        for ($i = 0; $i -lt $conf.Length; $i++) {
            if ($conf[$i] -match "^\s*addons_path\s*=") {
                $addonsLineFound = $true
                if ($conf[$i] -match [regex]::Escape($addonsPath)) {
                    Write-ColorOutput "SUCCESS: addons_path already includes custom_addons" "Green"
                } else {
                    $conf[$i] = $conf[$i].TrimEnd() + ",$addonsPath"
                    Write-ColorOutput "SUCCESS: Updated addons_path in configuration" "Green"
                }
                break
            }
        }
        
        if (-not $addonsLineFound) {
            $conf += "addons_path = $addonsPath"
            Write-ColorOutput "SUCCESS: Added new addons_path entry" "Green"
        }
        
        $conf | Set-Content $confFile -Encoding UTF8
        
    } catch {
        Write-ColorOutput "ERROR: Failed to update configuration: $($_.Exception.Message)" "Red"
        exit 1
    }
} else {
    Write-ColorOutput "ERROR: Configuration file not found: $confFile" "Red"
    Write-ColorOutput "Please create the configuration file manually." "Yellow"
}

# === Restart Odoo Service ===
if (-not $SkipRestart) {
    Write-ColorOutput "Restarting Odoo service..." "Yellow"
    
    try {
        Restart-Service -Name $selectedService -Force -ErrorAction Stop
        
        # Wait a moment and check service status
        Start-Sleep -Seconds 3
        $serviceStatus = Get-Service -Name $selectedService
        
        if ($serviceStatus.Status -eq 'Running') {
            Write-ColorOutput "SUCCESS: Service restarted successfully and is running" "Green"
        } else {
            Write-ColorOutput "WARNING: Service restarted but status is: $($serviceStatus.Status)" "Yellow"
        }
        
    } catch {
        Write-ColorOutput "ERROR: Failed to restart service: $($_.Exception.Message)" "Red"
        Write-ColorOutput "You may need to restart the service manually." "Yellow"
    }
} else {
    Write-ColorOutput "INFO: Skipping service restart (as requested)" "Yellow"
}

# === Summary ===
Write-ColorOutput "`n=== Installation Complete! ===" "Green"
Write-ColorOutput "Modules location: $addonsPath" "Cyan"
Write-ColorOutput "Configuration: $confFile" "Cyan"
Write-ColorOutput "Service: $selectedService" "Cyan"
Write-ColorOutput "`nNext steps:" "Yellow"
Write-ColorOutput "1. Log into Odoo admin panel" "White"
Write-ColorOutput "2. Go to Apps menu" "White"
Write-ColorOutput "3. Update the app list" "White"
Write-ColorOutput "4. Install your custom modules" "White"
