# Simple test script to debug path detection
param(
    [string]$ServiceName = "odoo-server-16.0"
)

Write-Host "Testing path detection for service: $ServiceName" -ForegroundColor Cyan

# Test NSSM paths
$nssmPaths = @(
    "C:\odoo15\nssm\win64\nssm.exe",
    "C:\odoo16\nssm\win64\nssm.exe", 
    "C:\odoo17\nssm\win64\nssm.exe",
    "C:\odoo18\nssm\win64\nssm.exe"
)

foreach ($nssmPath in $nssmPaths) {
    if (Test-Path $nssmPath) {
        Write-Host "Found NSSM at: $nssmPath" -ForegroundColor Green
        
        try {
            $nssmOutput = & $nssmPath get $ServiceName Application 2>$null
            Write-Host "Raw NSSM output:" -ForegroundColor Yellow
            Write-Host $nssmOutput -ForegroundColor White
            
            if ($nssmOutput) {
                # Clean up the output
                $lines = $nssmOutput -split "`n" | Where-Object { $_.Trim() -ne "" }
                Write-Host "Cleaned lines:" -ForegroundColor Yellow
                foreach ($line in $lines) {
                    Write-Host "  '$line'" -ForegroundColor White
                }
                
                # Find Python executable
                $odooExe = $null
                foreach ($line in $lines) {
                    $cleanLine = $line.Trim().Trim('"')
                    if ($cleanLine -match "python\.exe$" -and $cleanLine -notmatch "Access is denied") {
                        $odooExe = $cleanLine
                        Write-Host "Found Python exe: $odooExe" -ForegroundColor Green
                        break
                    }
                }
                
                if ($odooExe -and (Test-Path $odooExe)) {
                    $odooBase = Split-Path $odooExe -Parent | Split-Path -Parent
                    Write-Host "Detected Odoo base: $odooBase" -ForegroundColor Green
                    
                    # Check if this looks like a valid Odoo installation
                    $serverPath = Join-Path $odooBase "server"
                    if (Test-Path $serverPath) {
                        Write-Host "Valid Odoo installation found!" -ForegroundColor Green
                    } else {
                        Write-Host "Server directory not found in $odooBase" -ForegroundColor Red
                    }
                } else {
                    Write-Host "Python executable not found or invalid: $odooExe" -ForegroundColor Red
                }
            }
        } catch {
            Write-Host "Error running NSSM: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        break
    }
}
