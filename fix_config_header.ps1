# Fix missing bracket in odoo.conf - Run as Administrator
param(
    [string]$OdooPath = "C:\Program Files\Odoo 19.0.20250927"
)

$confFile = Join-Path $OdooPath "server\odoo.conf"

Write-Host "Fixing config file header..." -ForegroundColor Yellow

# Read all lines
$lines = Get-Content $confFile

# Fix the first line if it's missing the opening bracket
if ($lines[0] -eq "options]") {
    $lines[0] = "[options]"
    Write-Host "Fixed missing opening bracket in [options] section" -ForegroundColor Green
}

# Write back without BOM
$utf8NoBom = New-Object System.Text.UTF8Encoding $false
$content = $lines -join "`r`n"
[System.IO.File]::WriteAllText($confFile, $content, $utf8NoBom)

Write-Host "Config file fixed!" -ForegroundColor Green
Write-Host "First line is now: $($lines[0])" -ForegroundColor Cyan
