# Debug the exact condition
$cleanLine = "C:\Program Files\Odoo 19.0.20250927\python\python.exe"

Write-Host "Testing line: '$cleanLine'" -ForegroundColor Cyan
Write-Host "Length: $($cleanLine.Length)" -ForegroundColor Yellow

# Test each condition
$test1 = $cleanLine -like "*python.exe"
$test2 = $cleanLine -notlike "*Access is denied*"
$test3 = $cleanLine.Length -gt 10

Write-Host "Condition 1 (*python.exe): $test1" -ForegroundColor $(if($test1){"Green"}else{"Red"})
Write-Host "Condition 2 (not Access denied): $test2" -ForegroundColor $(if($test2){"Green"}else{"Red"})
Write-Host "Condition 3 (length > 10): $test3" -ForegroundColor $(if($test3){"Green"}else{"Red"})

$allTrue = $test1 -and $test2 -and $test3
Write-Host "All conditions: $allTrue" -ForegroundColor $(if($allTrue){"Green"}else{"Red"})

if ($allTrue) {
    $pythonDir = Split-Path $cleanLine -Parent
    $odooBase = Split-Path $pythonDir -Parent
    Write-Host "Python dir: $pythonDir" -ForegroundColor Cyan
    Write-Host "Odoo base: $odooBase" -ForegroundColor Green
}
