# Debug script to test service detection
$selectedService = "odoo-server-19.0"

Write-Host "Testing service detection for: $selectedService" -ForegroundColor Cyan

try {
    # Get service info
    $serviceInfo = Get-WmiObject -Class Win32_Service | Where-Object { $_.Name -eq $selectedService }
    Write-Host "Service found: $($serviceInfo -ne $null)" -ForegroundColor Yellow
    
    if ($serviceInfo -and $serviceInfo.PathName) {
        $servicePath = $serviceInfo.PathName.Trim().Trim('"')
        Write-Host "Service path: '$servicePath'" -ForegroundColor Green
        
        # Check if it's NSSM
        if ($servicePath -like "*nssm.exe*") {
            Write-Host "This is an NSSM service" -ForegroundColor Green
            
            # Test NSSM command
            Write-Host "Testing NSSM command..." -ForegroundColor Yellow
            $nssmOutput = & $servicePath get $selectedService Application 2>$null
            Write-Host "NSSM output: '$nssmOutput'" -ForegroundColor White
            
            if ($nssmOutput) {
                $lines = $nssmOutput -split "`n" | Where-Object { $_.Trim() -ne "" }
                Write-Host "Number of lines: $($lines.Count)" -ForegroundColor Yellow
                
                foreach ($line in $lines) {
                    $cleanLine = $line.Trim().Trim('"')
                    Write-Host "Processing line: '$cleanLine'" -ForegroundColor White
                    
                    if ($cleanLine -like "*python.exe" -and $cleanLine -notlike "*Access is denied*" -and $cleanLine.Length -gt 10) {
                        Write-Host "Found Python exe: $cleanLine" -ForegroundColor Green
                        
                        # Extract paths
                        $pythonDir = Split-Path $cleanLine -Parent
                        $odooBase = Split-Path $pythonDir -Parent
                        Write-Host "Python dir: $pythonDir" -ForegroundColor Cyan
                        Write-Host "Odoo base: $odooBase" -ForegroundColor Green
                        break
                    }
                }
            } else {
                Write-Host "No NSSM output received" -ForegroundColor Red
            }
        } else {
            Write-Host "Not an NSSM service" -ForegroundColor Red
        }
    } else {
        Write-Host "No service path found" -ForegroundColor Red
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
