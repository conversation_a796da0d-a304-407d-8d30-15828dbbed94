# Fix BOM issue in odoo.conf - Run as Administrator
param(
    [string]$OdooPath = "C:\Program Files\Odoo 19.0.20250927"
)

$confFile = Join-Path $OdooPath "server\odoo.conf"

Write-Host "Fixing BOM issue in odoo.conf..." -ForegroundColor Yellow

# Read the file content
$content = Get-Content $confFile -Raw

# Remove BOM if present
if ($content.StartsWith([char]0xFEFF)) {
    $content = $content.Substring(1)
    Write-Host "Removed BOM from config file" -ForegroundColor Green
}

# Write without BOM using UTF8NoBOM
$utf8NoBom = New-Object System.Text.UTF8Encoding $false
[System.IO.File]::WriteAllText($confFile, $content, $utf8NoBom)

Write-Host "Config file fixed!" -ForegroundColor Green
Write-Host "Now try starting the Odoo service" -ForegroundColor Cyan
