# Simple Odoo Module Installer - No overcomplicated logic
param(
    [string]$GitHubToken = "*********************************************************************************************",
    [string]$OdooPath,
    [string]$ServiceName,
    [switch]$SkipRestart
)

$REPO_URL = "https://github.com/cubesteam/kayan_erp.git"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

Write-ColorOutput "Starting Odoo Module Installer..." "Cyan"

# === Detect Odoo Services ===
if ($ServiceName) {
    $selectedService = $ServiceName
    Write-ColorOutput "Using provided service: $selectedService" "Green"
} else {
    $odooServices = Get-Service | Where-Object { $_.Name -like "*odoo*" -and $_.Name -notlike "*PostgreSQL*" }
    
    if ($odooServices.Count -eq 0) {
        Write-ColorOutput "ERROR: No Odoo services found." "Red"
        exit 1
    } elseif ($odooServices.Count -eq 1) {
        $selectedService = $odooServices[0].Name
        Write-ColorOutput "Found single Odoo service: $selectedService" "Green"
    } else {
        Write-ColorOutput "Multiple Odoo services detected:" "Yellow"
        for ($i = 0; $i -lt $odooServices.Count; $i++) {
            Write-ColorOutput "$($i+1). $($odooServices[$i].DisplayName) [$($odooServices[$i].Name)]" "White"
        }
        $selection = Read-Host "Enter the number of the Odoo instance you want to work with"
        $index = [int]$selection - 1
        if ($index -ge 0 -and $index -lt $odooServices.Count) {
            $selectedService = $odooServices[$index].Name
        } else {
            Write-ColorOutput "ERROR: Invalid selection. Exiting." "Red"
            exit 1
        }
    }
}

# === Detect Odoo Path ===
if ($OdooPath) {
    $odooBase = $OdooPath
    Write-ColorOutput "Using provided Odoo path: $odooBase" "Green"
} else {
    Write-ColorOutput "Detecting Odoo installation path..." "Yellow"
    
    # Get service info and find NSSM
    $serviceInfo = Get-WmiObject -Class Win32_Service | Where-Object { $_.Name -eq $selectedService }
    Write-ColorOutput "DEBUG: Service found: $($serviceInfo -ne $null)" "Gray"

    if ($serviceInfo -and $serviceInfo.PathName -like "*nssm.exe*") {
        $nssmPath = $serviceInfo.PathName.Trim().Trim('"')
        Write-ColorOutput "DEBUG: NSSM path: $nssmPath" "Gray"

        # Get the actual application path from NSSM
        try {
            $appPath = & $nssmPath get $selectedService Application 2>$null
            Write-ColorOutput "DEBUG: Raw app path: '$appPath'" "Gray"

            if ($appPath) {
                # Force to string and clean up
                $appPath = [string]$appPath
                $appPath = $appPath.Trim()
                Write-ColorOutput "DEBUG: After Trim: '$appPath'" "Gray"
                Write-ColorOutput "DEBUG: EndsWith space: $($appPath.EndsWith(' '))" "Gray"

                # Remove any trailing spaces manually
                while ($appPath.EndsWith(" ")) {
                    $appPath = $appPath.Substring(0, $appPath.Length - 1)
                    Write-ColorOutput "DEBUG: Removing space, new length: $($appPath.Length)" "Gray"
                }
                Write-ColorOutput "DEBUG: Final cleaned path: '$appPath'" "Gray"
                Write-ColorOutput "DEBUG: Final length: $($appPath.Length)" "Gray"

                if ($appPath -like "*python.exe") {
                    # Extract Odoo base: C:\Program Files\Odoo 19.0.20250927\python\python.exe -> C:\Program Files\Odoo 19.0.20250927
                    $pythonDir = Split-Path $appPath -Parent
                    $odooBase = Split-Path $pythonDir -Parent
                    Write-ColorOutput "SUCCESS: Auto-detected Odoo path: $odooBase" "Green"
                }
            } else {
                Write-ColorOutput "DEBUG: No app path returned" "Gray"
            }
        } catch {
            Write-ColorOutput "DEBUG: NSSM query failed: $($_.Exception.Message)" "Red"
        }
    } else {
        Write-ColorOutput "DEBUG: Service not found or not NSSM" "Gray"
    }
    
    # If auto-detection failed, ask user
    if (-not $odooBase) {
        $odooBase = Read-Host "Enter your Odoo installation path"
        if ([string]::IsNullOrWhiteSpace($odooBase)) {
            Write-ColorOutput "ERROR: No Odoo path provided. Exiting." "Red"
            exit 1
        }
    }
}

# Validate Odoo path
if (-not (Test-Path $odooBase)) {
    Write-ColorOutput "ERROR: Odoo path does not exist: $odooBase" "Red"
    exit 1
}

$serverPath = Join-Path $odooBase "server"
if (-not (Test-Path $serverPath)) {
    Write-ColorOutput "ERROR: Server directory not found in: $odooBase" "Red"
    exit 1
}

# === Define Paths ===
$addonsPath = Join-Path $odooBase "server\custom_addons"
$confFile = Join-Path $odooBase "server\odoo.conf"

Write-ColorOutput "Addons path: $addonsPath" "Cyan"
Write-ColorOutput "Config file: $confFile" "Cyan"

# === Create addons folder ===
if (!(Test-Path $addonsPath)) {
    New-Item -ItemType Directory -Force -Path $addonsPath | Out-Null
    Write-ColorOutput "Created addons folder: $addonsPath" "Green"
}

# === Clone Repository ===
Write-ColorOutput "Managing repository..." "Yellow"
$repoWithAuth = $REPO_URL -replace "https://", "https://$GitHubToken@"

$gitDir = Join-Path $addonsPath ".git"
if (Test-Path $gitDir) {
    Write-ColorOutput "Updating existing repository..." "Yellow"
    $currentLocation = Get-Location
    Set-Location $addonsPath
    git pull
    Set-Location $currentLocation
    Write-ColorOutput "Repository updated" "Green"
} else {
    Write-ColorOutput "Cloning repository..." "Yellow"
    git clone $repoWithAuth $addonsPath
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "Repository cloned successfully" "Green"
    } else {
        Write-ColorOutput "ERROR: Failed to clone repository" "Red"
        exit 1
    }
}

# === Update odoo.conf ===
Write-ColorOutput "Updating Odoo configuration..." "Yellow"

if (Test-Path $confFile) {
    # Create backup
    $backupFile = "$confFile.bak.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $confFile $backupFile -Force
    Write-ColorOutput "Backup created: $backupFile" "Green"
    
    $conf = Get-Content $confFile
    $addonsLineFound = $false
    
    for ($i = 0; $i -lt $conf.Length; $i++) {
        if ($conf[$i] -match "^\s*addons_path\s*=") {
            $addonsLineFound = $true
            if ($conf[$i] -notmatch [regex]::Escape($addonsPath)) {
                $conf[$i] = $conf[$i].TrimEnd() + ",$addonsPath"
                Write-ColorOutput "Updated addons_path in configuration" "Green"
            } else {
                Write-ColorOutput "addons_path already includes custom_addons" "Green"
            }
            break
        }
    }
    
    if (-not $addonsLineFound) {
        $conf += "addons_path = $addonsPath"
        Write-ColorOutput "Added new addons_path entry" "Green"
    }
    
    $conf | Set-Content $confFile -Encoding UTF8
} else {
    Write-ColorOutput "ERROR: Configuration file not found: $confFile" "Red"
}

# === Restart Service ===
if (-not $SkipRestart) {
    Write-ColorOutput "Restarting Odoo service..." "Yellow"
    
    # Try to use NSSM if available
    $nssmPath = Join-Path $odooBase "nssm\win64\nssm.exe"
    if (Test-Path $nssmPath) {
        & $nssmPath restart $selectedService
        Write-ColorOutput "Service restarted using NSSM" "Green"
    } else {
        # Fallback to PowerShell
        try {
            Restart-Service -Name $selectedService -Force
            Write-ColorOutput "Service restarted successfully" "Green"
        } catch {
            Write-ColorOutput "ERROR: Failed to restart service: $($_.Exception.Message)" "Red"
            Write-ColorOutput "Please restart the service manually" "Yellow"
        }
    }
} else {
    Write-ColorOutput "Skipping service restart" "Yellow"
}

# === Summary ===
Write-ColorOutput "`n=== Installation Complete! ===" "Green"
Write-ColorOutput "Modules location: $addonsPath" "Cyan"
Write-ColorOutput "Configuration: $confFile" "Cyan"
Write-ColorOutput "Service: $selectedService" "Cyan"
Write-ColorOutput "`nNext steps:" "Yellow"
Write-ColorOutput "1. Log into Odoo admin panel" "White"
Write-ColorOutput "2. Go to Apps menu" "White"
Write-ColorOutput "3. Update the app list" "White"
Write-ColorOutput "4. Install your custom modules" "White"
